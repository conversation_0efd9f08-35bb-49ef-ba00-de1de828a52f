import { Component, Inject, OnInit } from '@angular/core';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslationService, TranslationKeys } from '../services/translation.service';
import { TouchscreenDeviceService, DropdownDto } from '../services/touchscreen-device.service';
import { TouchscreenDevice } from '../services/warehouse-name.service';
import { AuthService } from '../services/auth.service';
import { LoginDialogComponent } from '../login-dialog/login-dialog.component';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-warehouse-name-dialog',
  templateUrl: './warehouse-name-dialog.component.html',
  styleUrls: ['./warehouse-name-dialog.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    FormsModule
  ]
})
export class WarehouseNameDialogComponent implements OnInit {
  selectedDevice: TouchscreenDevice | null = null;
  touchscreenDevices: DropdownDto[] = [];
  isLoading: boolean = true;
  loadError: string = '';
  isPostLoginLoading: boolean = false;
  t: TranslationKeys = {};

  constructor(
    public dialogRef: MatDialogRef<WarehouseNameDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private translationService: TranslationService,
    private touchscreenDeviceService: TouchscreenDeviceService,
    private authService: AuthService,
    private dialog: MatDialog
  ) {
    this.t = this.translationService.getTranslations();
    console.log('Warehouse dialog translations:', this.t);
  }

  ngOnInit(): void {
    this.loadTouchscreenDevices();

    // Pre-fill with existing device if available
    if (this.data?.existingName && this.data?.existingId) {
      this.selectedDevice = {
        id: this.data.existingId,
        name: this.data.existingName
      };
    }
  }

  loadTouchscreenDevices(): void {
    this.isLoading = true;
    this.loadError = '';

    // Check if user is authenticated first
    if (!this.authService.isAuthenticated()) {
      this.showLoginDialog();
      return;
    }

    this.touchscreenDeviceService.getTouchscreenDevicesList().subscribe({
      next: (devices) => {
        this.touchscreenDevices = devices;
        this.isLoading = false;

        // If we have existing data, find and select the matching device
        if (this.data?.existingId) {
          const existingDevice = devices.find(d => d.id === this.data.existingId);
          if (existingDevice) {
            this.selectedDevice = existingDevice;
          }
        }
      },
      error: (error) => {
        console.error('Error loading touchscreen devices:', error);

        // If it's an authentication error, show login dialog
        if (error.status === 401 || error.status === 403) {
          this.showLoginDialog();
        } else {
          this.loadError = this.t['failedToLoadDevices'] || 'Failed to load touchscreen devices. Please try again.';
          this.isLoading = false;
        }
      }
    });
  }

  private showLoginDialog(): void {
    this.isLoading = false;

    const loginDialogRef = this.dialog.open(LoginDialogComponent, {
      width: '400px',
      disableClose: true,
      data: {
        username: '',
        password: ''
      }
    });

    loginDialogRef.afterClosed().subscribe(result => {
      console.log('Login dialog closed with result:', result);
      if (result) {
        // User entered credentials, attempt login
        console.log('Attempting login with credentials');
        this.authService.login(result).subscribe({
          next: (loginResponse) => {
            if (loginResponse.isAuthSuccessful) {
              console.log('Login successful');

              // Immediately start loading devices without showing intermediate states
              this.loadError = '';
              this.isLoading = true;
              this.isPostLoginLoading = false; // Skip the post-login message

              // Load devices directly without delay
              this.loadTouchscreenDevices();
            } else {
              console.error('Login failed:', loginResponse.errorMessage);
              this.loadError = loginResponse.errorMessage || this.t['loginFailed'] || 'Login failed. Please check your credentials and try again.';
            }
          },
          error: (loginError) => {
            console.error('Login failed:', loginError);
            this.loadError = this.t['loginFailed'] || 'Login failed. Please check your credentials and try again.';
          }
        });
      } else {
        // User cancelled login, show error
        this.loadError = this.t['authenticationRequired'] || 'Authentication required to load touchscreen devices.';
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    if (this.selectedDevice) {
      this.dialogRef.close(this.selectedDevice);
    }
  }

  isFormValid(): boolean {
    return !!this.selectedDevice;
  }

  onDeviceSelectionChange(): void {
    // This method can be used for any additional logic when device selection changes
    console.log('Selected device:', this.selectedDevice);
  }

  compareDevices(device1: DropdownDto, device2: DropdownDto): boolean {
    return device1 && device2 ? device1.id === device2.id : device1 === device2;
  }

  getLoadingMessage(): string {
    if (this.isPostLoginLoading) {
      return this.t['loadingDevicesAfterLogin'] || 'Успешен вход! Зареждане на устройства...';
    }
    return this.t['loadingDevices'] || 'Зареждане на устройства...';
  }
}