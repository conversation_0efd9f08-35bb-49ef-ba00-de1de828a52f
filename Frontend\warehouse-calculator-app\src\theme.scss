@use '@angular/material' as mat;

@include mat.core();

$warehouse-calculator-primary: mat.define-palette(mat.$indigo-palette);
$warehouse-calculator-accent: mat.define-palette(mat.$pink-palette, A200, A100, A400);
$warehouse-calculator-warn: mat.define-palette(mat.$red-palette);

$warehouse-calculator-theme: mat.define-light-theme((
  color: (
    primary: $warehouse-calculator-primary,
    accent: $warehouse-calculator-accent,
    warn: $warehouse-calculator-warn,
  )
));

@include mat.all-component-themes($warehouse-calculator-theme);