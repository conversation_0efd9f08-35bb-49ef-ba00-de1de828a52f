import { Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { Product } from './product.model';
import { TranslationService, TranslationKeys } from '../services/translation.service';
import { WarehouseNameService } from '../services/warehouse-name.service';
import { ProductService } from '../services/product.service';

@Component({
  selector: 'app-product',
  templateUrl: './product.component.html',
  styleUrls: ['./product.component.css']
})
export class ProductComponent implements OnInit {
  products: Product[] = [];
  filteredProducts: Product[] = [];
  searchTerm: string = '';
  currentPage: number = 1;
  itemsPerPage: number = 15; // Show all 15 products on one page for now
  selectedProductId: number | null = null; // For highlighting found product
  t: TranslationKeys = {};

  constructor(
    private router: Router, 
    private warehouseNameService: WarehouseNameService,
    private translationService: TranslationService,
    private productService: ProductService
  ) {
    this.t = this.translationService.getTranslations();
  }

  // Barcode scanning variables
  private barcodeBuffer: string = '';
  private barcodeTimeout: any;

  // Search history variables
  searchHistory: string[] = [];
  showSearchHistory: boolean = false;

  ngOnInit(): void {
    this.loadProducts();
    this.loadSearchHistory(); // Load search history from localStorage
    
    // Subscribe to translation changes
    this.translationService.currentLanguage$.subscribe(() => {
      this.t = this.translationService.getTranslations();
    });
  }

  loadProducts(): void {
    this.productService.getProducts().subscribe(products => {
      this.products = products;
      this.filteredProducts = this.products;
    });
  }

  // Handle barcode scanner input
  @HostListener('document:keypress', ['$event'])
  handleKeyPress(event: KeyboardEvent): void {
    // Ignore if user is typing in search field
    if (event.target instanceof HTMLInputElement) {
      return;
    }

    // Add character to buffer
    this.barcodeBuffer += event.key;
    
    // Clear previous timeout
    if (this.barcodeTimeout) {
      clearTimeout(this.barcodeTimeout);
    }
    
    // Set timeout to process barcode after a delay (indicating end of scan)
    this.barcodeTimeout = setTimeout(() => {
      this.processBarcode();
    }, 100); // 100ms delay to detect end of barcode scan
  }

  private processBarcode(): void {
    if (this.barcodeBuffer.length > 0) {
      console.log('Barcode scanned:', this.barcodeBuffer);
      
      // Search for product by barcode
      this.productService.getProductByBarcode(this.barcodeBuffer).subscribe(product => {
        if (product) {
          console.log('Product found:', product);
          // Navigate to calculator with the found product
          this.onProductClick(product);
        } else {
          console.log('Product not found for barcode:', this.barcodeBuffer);
          // Could show a notification here
        }
      });
      
      // Clear buffer
      this.barcodeBuffer = '';
    }
  }

  onProductClick(product: Product): void {
    console.log('Product clicked:', product);
    
    // Clear selection if the clicked product was the selected one
    if (this.selectedProductId === product.id) {
      this.selectedProductId = null;
    }
    
    // Navigate to calculator with product data
    this.router.navigate(['/calculator'], { 
      queryParams: {
        productId: product.id,
        productName: product.name,
        productCode: product.code,
        productImage: product.imageUrl,
        productSequentialNumber: product.sequentialNumber,
        productCategory: product.category,
        productBarcode: product.barcode
      }
    });
  }

  onSearchButtonClick(): void {
    localStorage.setItem('STORAGE_KEY_NAME', '');
    localStorage.setItem('STORAGE_KEY_ID', '');
    if (this.searchTerm.trim()) {
      const searchTermToSave = this.searchTerm; // Store the search term before clearing
      
      this.productService.searchProducts(this.searchTerm).subscribe(products => {
        if (products.length > 0) {
          // Find the first matching product in the full product list
          const foundProduct = products[0];
          const productIndex = this.products.findIndex(p => p.id === foundProduct.id);
          
          if (productIndex !== -1) {
            // Calculate which page contains this product
            const pageNumber = Math.floor(productIndex / this.itemsPerPage) + 1;
            this.currentPage = pageNumber;
            
            // Highlight the found product
            this.selectedProductId = foundProduct.id;
            
            // Clear the search field after successfully finding the product
            this.searchTerm = '';
          }
        }
        
        // Add to search history using the original search term
        this.addToSearchHistory(searchTermToSave);
      });
    } else {
      // If search term is empty, show all products
      this.loadProducts();
      this.selectedProductId = null;
    }
  }

  onClearSearch(): void {
    this.searchTerm = '';
    this.loadProducts();
    this.currentPage = 1;
    this.selectedProductId = null; // Clear any highlighting
  }

  onSearchFocus(): void {
    this.showSearchHistory = true;
  }

  onSearchBlur(): void {
    // Delay hiding to allow clicking on dropdown items
    setTimeout(() => {
      this.showSearchHistory = false;
    }, 200);
  }

  addToSearchHistory(searchTerm: string): void {
    if (searchTerm.trim()) {
      // Remove if already exists
      this.searchHistory = this.searchHistory.filter(item => item !== searchTerm);
      
      // Add to beginning
      this.searchHistory.unshift(searchTerm);
      
      // If history becomes more than 10 items, remove the oldest one
      if (this.searchHistory.length > 10) {
        this.searchHistory.pop(); // Remove the last (oldest) item
      }
      
      // Save to localStorage
      this.saveSearchHistory();
    }
  }

  private loadSearchHistory(): void {
    try {
      const savedHistory = localStorage.getItem('product_search_history');
      if (savedHistory) {
        this.searchHistory = JSON.parse(savedHistory);
      }
    } catch (error) {
      console.error('Error loading search history:', error);
      this.searchHistory = [];
    }
  }

  private saveSearchHistory(): void {
    try {
      localStorage.setItem('product_search_history', JSON.stringify(this.searchHistory));
    } catch (error) {
      console.error('Error saving search history:', error);
    }
  }

  selectFromHistory(event: any): void {
    // MatSelect event has a 'value' property
    const term = event?.value;
    if (term && typeof term === 'string') {
      this.searchTerm = term;
      this.onSearchButtonClick();
      this.showSearchHistory = false;
    }
  }

  clearSearchHistory(): void {
    this.searchHistory = [];
    this.saveSearchHistory(); // Also clear from localStorage
  }

  get paginatedProducts(): Product[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.products.slice(startIndex, endIndex); // Always show from all products, not filtered
  }

  get totalPages(): number {
    return Math.ceil(this.products.length / this.itemsPerPage); // Calculate based on all products
  }

  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  canGoPrevious(): boolean {
    return this.currentPage > 1;
  }

  canGoNext(): boolean {
    return this.currentPage < this.totalPages;
  }
} 