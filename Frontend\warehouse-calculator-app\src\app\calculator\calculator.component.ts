import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { Product } from '../product/product.model';
import { TranslationService, TranslationKeys } from '../services/translation.service';
import { LoginDialogComponent, LoginDialogData } from '../login-dialog/login-dialog.component';

@Component({
  selector: 'app-calculator',
  templateUrl: './calculator.component.html',
  styleUrls: ['./calculator.component.css']
})
export class CalculatorComponent implements OnInit {
  selectedProduct: Product | null = null;
  showProductInfo: boolean = false;

  // Calculator input fields
  package: number = 0;
  tile: number = 0;
  m2: number = 0;
  totalM2: number = 0;
  t: TranslationKeys = {};

  constructor(
    private route: ActivatedRoute, 
    private router: Router,
    private translationService: TranslationService,
    private dialog: MatDialog
  ) {
    this.t = this.translationService.getTranslations();
  }

  ngOnInit(): void {
    this.loadProductFromQueryParams();
    
    // Subscribe to translation changes
    this.translationService.currentLanguage$.subscribe(() => {
      this.t = this.translationService.getTranslations();
    });
  }

  loadProductFromQueryParams(): void {
    this.route.queryParams.subscribe(params => {
      if (params['productId']) {
        this.selectedProduct = {
          id: Number(params['productId']),
          name: params['productName'] || '',
          code: params['productCode'] || '',
          imageUrl: params['productImage'] || '',
          sequentialNumber: params['productSequentialNumber'] ? Number(params['productSequentialNumber']) : undefined,
          category: params['productCategory'] || '',
          barcode: params['productBarcode'] || ''
        };
        this.showProductInfo = true;
      }
    });
  }

  clearProductSelection(): void {
    this.selectedProduct = null;
    this.showProductInfo = false;
  }

  onCancel(): void {
    // Navigate back to products page
    this.router.navigate(['/product']);
  }

  onPrint(): void {
    // Show login dialog first
    const dialogRef = this.dialog.open(LoginDialogComponent, {
      width: '400px',
      disableClose: true,
      data: {
        username: '',
        password: ''
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // User entered credentials, proceed with printing
        console.log('Login successful:', result.username);
        console.log('Print button clicked');
        console.log('Package:', this.package);
        console.log('Tile:', this.tile);
        console.log('M2:', this.m2);
        console.log('Total M2:', this.totalM2);
        // TODO: Add actual printing logic here
      } else {
        // User cancelled login
        console.log('Login cancelled');
      }
    });
  }
}