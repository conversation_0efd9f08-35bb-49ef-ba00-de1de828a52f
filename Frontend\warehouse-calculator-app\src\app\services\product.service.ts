import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Product } from '../product/product.model';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private products: Product[] = [
    {
      id: 1,
      name: 'Product 1',
      code: 'P001',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 208,
      category: 'Category A',
      barcode: '1234567890123'
    },
    {
      id: 2,
      name: 'Product 2',
      code: 'P002',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 209,
      category: 'Category B',
      barcode: '1234567890124'
    },
    {
      id: 3,
      name: 'Product 3',
      code: 'P003',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 300,
      category: 'Category A',
      barcode: '1234567890125'
    },
    {
      id: 4,
      name: 'Product 4',
      code: 'P004',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 301,
      category: 'Category C',
      barcode: '1234567890126'
    },
    {
      id: 5,
      name: 'Product 5',
      code: 'P005',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 302,
      category: 'Category B',
      barcode: '1234567890127'
    },
    {
      id: 6,
      name: 'Product 6',
      code: 'P006',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 303,
      category: 'Category A',
      barcode: '1234567890128'
    },
    {
      id: 7,
      name: 'Product 7',
      code: 'P007',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 304,
      category: 'Category C',
      barcode: '1234567890129'
    },
    {
      id: 8,
      name: 'Product 8',
      code: 'P008',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 305,
      category: 'Category B',
      barcode: '1234567890130'
    },
    {
      id: 9,
      name: 'Product 9',
      code: 'P009',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 306,
      category: 'Category A',
      barcode: '1234567890131'
    },
    {
      id: 10,
      name: 'Product 10',
      code: 'P010',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 307,
      category: 'Category C',
      barcode: '1234567890132'
    },
    {
      id: 11,
      name: 'Product 11',
      code: 'P011',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 308,
      category: 'Category B',
      barcode: '1234567890133'
    },
    {
      id: 12,
      name: 'Product 12',
      code: 'P012',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 309,
      category: 'Category A',
      barcode: '1234567890134'
    },
    {
      id: 13,
      name: 'Product 13',
      code: 'P013',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 310,
      category: 'Category C',
      barcode: '1234567890135'
    },
    {
      id: 14,
      name: 'Product 14',
      code: 'P014',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 311,
      category: 'Category B',
      barcode: '1234567890136'
    },
    {
      id: 15,
      name: 'Product 15',
      code: 'P015',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 312,
      category: 'Category A',
      barcode: '1234567890137'
    },
    {
      id: 16,
      name: 'Product 16',
      code: 'P016',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 313,
      category: 'Category A',
      barcode: '1234567890138'
    },
    {
      id: 17,
      name: 'Product 17',
      code: 'P017',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 314,
      category: 'Category A',
      barcode: '1234567890139'
    },
    {
      id: 18,
      name: 'Product 18',
      code: 'P018',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 315,
      category: 'Category A',
      barcode: '1234567890140'
    },
    {
      id: 19,
      name: 'Product 19',
      code: 'P019',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 316,
      category: 'Category A',
      barcode: '1234567890141'
    },
    {
      id: 20,
      name: 'Product 20',
      code: 'P020',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 317,
      category: 'Category A',
      barcode: '1234567890142'
    },
    {
      id: 21,
      name: 'Product 21',
      code: 'P021',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 318,
      category: 'Category A',
      barcode: '1234567890143'
    },
    {
      id: 22,
      name: 'Product 22',
      code: 'P022',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 319,
      category: 'Category A',
      barcode: '1234567890144'
    },
    {
      id: 23,
      name: 'Product 23',
      code: 'P023',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 320,
      category: 'Category A',
      barcode: '1234567890145'
    },
    {
      id: 24,
      name: 'Product 24',
      code: 'P024',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 321,
      category: 'Category A',
      barcode: '1234567890146'
    },
    {
      id: 25,
      name: 'Product 25',
      code: 'P025',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 322,
      category: 'Category A',
      barcode: '1234567890147'
    },
    {
      id: 26,
      name: 'Product 26',
      code: 'P026',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 323,
      category: 'Category A',
      barcode: '1234567890148'
    },
    {
      id: 27,
      name: 'Product 27',
      code: 'P027',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 324,
      category: 'Category A',
      barcode: '1234567890149'
    },
    {
      id: 28,
      name: 'Product 28',
      code: 'P028',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 325,
      category: 'Category A',
      barcode: '1234567890150'
    },
    {
      id: 29,
      name: 'Product 29',
      code: 'P029',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 326,
      category: 'Category A',
      barcode: '1234567890151'
    },
    {
      id: 30,
      name: 'Product 30',
      code: 'P030',
      imageUrl: 'assets/images/tile.jpg',
      sequentialNumber: 327,
      category: 'Category A',
      barcode: '1234567890152'
    }
  ];

  constructor() { }

  getProducts(): Observable<Product[]> {
    return of(this.products);
  }

  getProductByBarcode(barcode: string): Observable<Product | null> {
    const product = this.products.find(p => p.barcode === barcode);
    return of(product || null);
  }

  searchProducts(searchTerm: string): Observable<Product[]> {
    if (!searchTerm.trim()) {
      return this.getProducts();
    }
    
    const filteredProducts = this.products.filter(product =>
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.barcode.includes(searchTerm)
    );
    
    return of(filteredProducts);
  }
} 