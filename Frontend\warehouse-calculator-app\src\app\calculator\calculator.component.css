.calculator-container {
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 60px;
}

.product-info-section {
  margin-bottom: 20px;
}

.product-info-card {
  background-color: #f8f9fa;
  border-left: 4px solid #1976d2;
}

.product-info-card mat-card-header {
  position: relative;
}

.close-button {
  position: absolute;
  top: 8px;
  right: 8px;
}

.product-details {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.product-image-container {
  flex-shrink: 0;
  width: 150px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.product-text-info {
  flex: 1;
}

.product-sequential-number {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 8px 0;
  text-align: center;
  line-height: 1.2;
}

.product-category,
.product-price {
  margin: 5px 0;
  font-size: 14px;
}

.calculator-card {
  margin-bottom: 20px;
}

/* Product Header Styles */
.product-header {
  background: linear-gradient(135deg, #3F51B5 0%, #5C6BC0 100%); /* Material indigo gradient */
  color: white;
  padding: 15px 20px; /* Standard padding */
  margin: 0 0 300px 0; /* No negative horizontal margins, just bottom spacing */
  border-radius: 4px;
}

.product-title {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 10px 15px;
  border-radius: 4px;
}

.product-details-row {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  margin-top: 10px;
}

.product-detail-item {
  flex: 1;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8px 10px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.product-detail-item .label {
  display: block;
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 4px;
  opacity: 0.9;
}

.product-detail-item .value {
  display: block;
  font-size: 24px;
  font-weight: bold;
}

.calculator-card ul {
  margin: 15px 0;
  padding-left: 20px;
}

 .calculator-card li {
   margin: 5px 0;
   font-size: 14px;
 }

 .calculator-inputs {
   display: flex;
   gap: 15px;
   margin: 20px 0;
   flex-wrap: wrap;
   padding: 0; /* Ensure no extra padding that could affect alignment */
 }

 .input-field {
   flex: 1;
   min-width: 200px;
 }

 .calculator-buttons {
   display: flex;
   justify-content: space-between;
   margin: 20px 0;
   gap: 15px;
 }

 .cancel-button,
 .print-button {
   min-width: 120px;
   height: 45px;
   font-size: 16px;
 }

 .product-data-reference {
   margin-top: 30px;
   padding: 15px;
   background-color: #f5f5f5;
   border-radius: 8px;
   border-left: 4px solid #1976d2;
 }

 /* Responsive design */
@media (max-width: 768px) {
  .product-details {
    flex-direction: column;
    gap: 15px;
  }
  
  .product-image-container {
    width: 100%;
    height: 120px;
  }

  .calculator-inputs {
    flex-direction: column;
  }

  .input-field {
    min-width: auto;
  }

  .calculator-buttons {
    flex-direction: column;
  }

  .cancel-button,
  .print-button {
    width: 100%;
  }

  /* Responsive header styles */
  .product-details-row {
    flex-direction: column;
    gap: 8px;
  }

  .product-title {
    font-size: 20px;
    padding: 8px 12px;
  }

  .product-detail-item {
    padding: 6px 8px;
  }

  .product-detail-item .label {
    font-size: 11px;
  }

  .product-detail-item .value {
    font-size: 13px;
  }
}