{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"warehouse-calculator-app": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css", "src/theme.scss"], "scripts": []}, "configurations": {"production": {"outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "warehouse-calculator-app:build:development", "port": 4201}, "configurations": {"production": {"browserTarget": "warehouse-calculator-app:build:production"}, "development": {"browserTarget": "warehouse-calculator-app:build:development"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "warehouse-calculator-app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css", "src/theme.scss"], "scripts": []}}}}}, "cli": {"analytics": false, "cache": {"enabled": false}}}