.product-card {
  width: 100%;
  height: 220px;
  cursor: pointer;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.product-card.selected {
  border: 3px solid #2196F3;
  background-color: #E3F2FD;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(33, 150, 243, 0.3);
  animation: pulse 1.5s ease-in-out;
}

@keyframes pulse {
  0% {
    box-shadow: 0 6px 12px rgba(33, 150, 243, 0.3);
  }
  50% {
    box-shadow: 0 8px 16px rgba(33, 150, 243, 0.5);
  }
  100% {
    box-shadow: 0 6px 12px rgba(33, 150, 243, 0.3);
  }
}

.product-name-section {
  padding: 12px 16px 8px 16px;
  flex-shrink: 0;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-code {
  font-size: 12px;
  color: #666;
  margin: 4px 0 0 0;
  font-weight: 500;
}

.product-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  min-height: 100px;
}

.product-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.product-sequential-number-section {
  padding: 8px 16px 12px 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-sequential-number {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0;
  text-align: center;
  line-height: 1.2;
}



.fallback-image {
  opacity: 0.7;
  filter: grayscale(50%);
} 