# Warehouse Calculator App

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 15.2.5.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.

## Start Backend from VSCode

1. Open terminal to 'Backend/ERP/Web.API'
2. Run `dotnet run --launch-profile "Web.API Stage DB"`

## Start Angular and Electron together

1. Run `npm run electron-dev` to start angular and electron app together

## Start Angular and Electron separately

1. Run `npx ng serve` to start angular
2. Run `npm run electron` to start electron

## Build Angular and electron for production and package 'warehouse-calculator-app.exe'

1. Run `npm run electron-build-prod` to build the angular and electron app
2. Run `npm run packager` to package the app with exe file in 'electron-packager' folder

## Getting Started

1. Install dependencies: `npm install`
2. Start development server: `npm run electron-dev`
3. Begin building your warehouse calculator features!