import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  constructor(private authService: AuthService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Get the auth token from the service
    const authToken = this.authService.getToken();

    // Skip adding token for certain requests (like login, translation files, etc.)
    if (this.shouldSkipToken(req.url)) {
      return next.handle(req);
    }

    // Clone the request and add the authorization header if token exists
    if (authToken) {
      const authReq = req.clone({
        headers: req.headers
          .set('Authorization', `Bearer ${authToken}`)
          .set('selectedOrgUnitId', '1')
      });
      return next.handle(authReq);
    }

    // If no token, proceed with original request
    return next.handle(req);
  }

  private shouldSkipToken(url: string): boolean {
    // Skip token for these types of requests
    const skipPatterns = [
      '/Users/<USER>',
      '/assets/',
      'assets/i18n/',
      '.json'
    ];

    return skipPatterns.some(pattern => url.includes(pattern));
  }
}
