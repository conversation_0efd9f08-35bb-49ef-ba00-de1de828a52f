<div class="warehouse-dialog-container">
  <!-- <h2 style="text-align: center;" mat-dialog-title>
    {{ t['welcomeToWarehouseCalculator'] || 'Добре дошли в Калкулатора за склад' }}
  </h2> -->

  <mat-dialog-content>
    <p style="text-align: center;">
      {{ t['selectTouchscreenDevice'] || 'Моля, изберете вашето устройство:' }}
    </p>

    <!-- Loading spinner -->
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>{{ getLoadingMessage() }}</p>
    </div>

    <!-- Error message -->
    <div *ngIf="loadError" class="error-container">
      <p class="error-message">{{ loadError }}</p>
      <button mat-button (click)="loadTouchscreenDevices()" class="retry-button">
        {{ t['retry'] || 'Опитай отново' }}
      </button>
    </div>

    <!-- Device selection dropdown -->
    <mat-form-field *ngIf="!isLoading && !loadError" appearance="outline" class="warehouse-name-field">
      <mat-label>{{ t['touchscreenDevice'] || 'Устройство' }}</mat-label>
      <mat-select
        [(ngModel)]="selectedDevice"
        (selectionChange)="onDeviceSelectionChange()"
        [compareWith]="compareDevices">
        <mat-option *ngFor="let device of touchscreenDevices" [value]="device">
          {{ device.name }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <!-- <button mat-button (click)="onCancel()">Cancel</button> -->
    <button mat-button="filled" (click)="onSave()" [disabled]="!isFormValid() || isLoading">
      {{ t['save'] || 'Запази' }}
    </button>
  </mat-dialog-actions>
</div>