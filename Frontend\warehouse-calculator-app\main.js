const { app, BrowserWindow, ipc<PERSON><PERSON>, Menu } = require('electron');
const createMenu = require('./electron-menu');

require("module").globalPaths.push(process.cwd() + '/node_modules');
const path = require('path');
const isDev = process.env.ELECTRON_IS_DEV;
const localEnvironment = process.env.ANGULAR_ENV;
const imgPath = path.join(__dirname, 'src', 'assets', 'images', 'alati-fav.png');
Menu.setApplicationMenu(null);

let mainWindow;

// Enable auto-reload when Angular app rebuilds in development mode
if (isDev == 1) {
  
  require('electron-reload')(__dirname, {
    electron: path.join(__dirname, 'node_modules', '.bin', 'electron'),
    hardResetMethod: 'exit'
  });
}


function createWindow() {
  mainWindow = new BrowserWindow({
    center: true,
    resizable: true,
    icon: imgPath,
    minimizable: true,
    fullScreenable: true,
    closable: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false
    },
  });

  mainWindow.maximize();
//to do remove the line for dev when the app is tested
  if (isDev == 1) {
    // Load Angular app from localhost in development mode
    app.commandLine.appendSwitch('ignore-certificate-errors');
    mainWindow.loadURL('http://localhost:4201').catch(() => {
      console.error('Failed to load Angular dev server at http://localhost:4201. Ensure it is running.');
    });
    mainWindow.webContents.openDevTools();  // Optional: open dev tools in development
  } else {
    // Load built Angular app from dist/ folder in production
    const htmlPath = path.join(__dirname, 'dist', 'index.html');
    mainWindow.loadFile(htmlPath).catch((error) => {
      console.error('Failed to load Angular app from dist folder:', error);
    });
  }
}

app.whenReady().then(createWindow);

// Ignore certificate errors for development
app.commandLine.appendSwitch('ignore-certificate-errors');

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});