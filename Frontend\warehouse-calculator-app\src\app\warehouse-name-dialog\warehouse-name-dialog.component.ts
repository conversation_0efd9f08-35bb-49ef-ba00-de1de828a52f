import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslationService, TranslationKeys } from '../services/translation.service';

@Component({
  selector: 'app-warehouse-name-dialog',
  templateUrl: './warehouse-name-dialog.component.html',
  styleUrls: ['./warehouse-name-dialog.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    FormsModule
  ]
})
export class WarehouseNameDialogComponent {
  warehouseName: string = '';
  t: TranslationKeys = {};

  constructor(
    public dialogRef: MatDialogRef<WarehouseNameDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private translationService: TranslationService
  ) {
    // Pre-fill with existing name if available
    this.warehouseName = data?.existingName || '';
    this.t = this.translationService.getTranslations();
    console.log('Warehouse dialog translations:', this.t);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    if (this.warehouseName.trim()) {
      this.dialogRef.close(this.warehouseName.trim());
    }
  }

  isFormValid(): boolean {
    return this.warehouseName.trim().length > 0;
  }
} 