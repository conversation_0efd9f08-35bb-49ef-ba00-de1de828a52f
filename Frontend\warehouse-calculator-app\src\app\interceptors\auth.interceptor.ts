import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, switchMap, filter, take } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  constructor(private authService: AuthService) {}

  intercept(req: HttpRequest<any>, next: HttpHand<PERSON>): Observable<HttpEvent<any>> {
    // Skip adding token for certain requests (like login, translation files, etc.)
    if (this.shouldSkipToken(req.url)) {
      return next.handle(req);
    }

    // Add auth headers to the request
    const authReq = this.addAuthHeaders(req);

    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        // If we get a 401 and it's not a login request, try to refresh the token
        if (error.status === 401 && !req.url.includes('/Users/<USER>')) {
          return this.handle401Error(authReq, next);
        }

        return throwError(() => error);
      })
    );
  }

  private addAuthHeaders(req: HttpRequest<any>): HttpRequest<any> {
    const authToken = this.authService.getToken();

    if (authToken) {
      return req.clone({
        headers: req.headers
          .set('Authorization', `Bearer ${authToken}`)
          .set('selectedOrgUnitId', '1')
      });
    }

    return req;
  }

  private handle401Error(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this.authService.refreshToken().pipe(
        switchMap((response) => {
          this.isRefreshing = false;

          if (response.isAuthSuccessful) {
            this.refreshTokenSubject.next(response.accessToken);

            // Retry the original request with the new token
            const newAuthReq = this.addAuthHeaders(req);
            return next.handle(newAuthReq);
          } else {
            // Refresh failed, clear tokens and throw error
            this.authService.logout();
            return throwError(() => new Error(response.errorMessage));
          }
        }),
        catchError((error) => {
          this.isRefreshing = false;
          // Refresh failed, user needs to login again
          return throwError(() => error);
        })
      );
    } else {
      // If refresh is already in progress, wait for it to complete
      return this.refreshTokenSubject.pipe(
        filter(token => token != null),
        take(1),
        switchMap(() => {
          const newAuthReq = this.addAuthHeaders(req);
          return next.handle(newAuthReq);
        })
      );
    }
  }

  private shouldSkipToken(url: string): boolean {
    // Skip token for these types of requests
    const skipPatterns = [
      '/Users/<USER>',
      '/Users/<USER>',
      '/assets/',
      'assets/i18n/',
      '.json'
    ];

    return skipPatterns.some(pattern => url.includes(pattern));
  }
}
