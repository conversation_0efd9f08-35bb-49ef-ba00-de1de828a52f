import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslationService, TranslationKeys } from '../services/translation.service';
import { TouchscreenDeviceService, DropdownDto } from '../services/touchscreen-device.service';
import { TouchscreenDevice } from '../services/warehouse-name.service';

@Component({
  selector: 'app-warehouse-name-dialog',
  templateUrl: './warehouse-name-dialog.component.html',
  styleUrls: ['./warehouse-name-dialog.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    FormsModule
  ]
})
export class WarehouseNameDialogComponent implements OnInit {
  selectedDevice: TouchscreenDevice | null = null;
  touchscreenDevices: DropdownDto[] = [];
  isLoading: boolean = true;
  loadError: string = '';
  t: TranslationKeys = {};

  constructor(
    public dialogRef: MatDialogRef<WarehouseNameDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private translationService: TranslationService,
    private touchscreenDeviceService: TouchscreenDeviceService
  ) {
    this.t = this.translationService.getTranslations();
    console.log('Warehouse dialog translations:', this.t);
  }

  ngOnInit(): void {
    this.loadTouchscreenDevices();

    // Pre-fill with existing device if available
    if (this.data?.existingName && this.data?.existingId) {
      this.selectedDevice = {
        id: this.data.existingId,
        name: this.data.existingName
      };
    }
  }

  loadTouchscreenDevices(): void {
    this.isLoading = true;
    this.loadError = '';

    this.touchscreenDeviceService.getTouchscreenDevicesList().subscribe({
      next: (devices) => {
        this.touchscreenDevices = devices;
        this.isLoading = false;

        // If we have existing data, find and select the matching device
        if (this.data?.existingId) {
          const existingDevice = devices.find(d => d.id === this.data.existingId);
          if (existingDevice) {
            this.selectedDevice = existingDevice;
          }
        }
      },
      error: (error) => {
        console.error('Error loading touchscreen devices:', error);
        this.loadError = 'Failed to load touchscreen devices. Please try again.';
        this.isLoading = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    if (this.selectedDevice) {
      this.dialogRef.close(this.selectedDevice);
    }
  }

  isFormValid(): boolean {
    return !!this.selectedDevice;
  }

  onDeviceSelectionChange(): void {
    // This method can be used for any additional logic when device selection changes
    console.log('Selected device:', this.selectedDevice);
  }

  compareDevices(device1: DropdownDto, device2: DropdownDto): boolean {
    return device1 && device2 ? device1.id === device2.id : device1 === device2;
  }
}