import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { WarehouseNameService } from './services/warehouse-name.service';
import { TranslationService, TranslationKeys } from './services/translation.service';
import { WarehouseNameDialogComponent } from './warehouse-name-dialog/warehouse-name-dialog.component';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  title = 'warehouse-calculator-app';
  warehouseName: string = '';
  currentPageName: string = '';
  t: TranslationKeys;

  constructor(
    private warehouseNameService: WarehouseNameService,
    private translationService: TranslationService,
    private dialog: MatDialog,
    private router: Router
  ) {
    this.t = this.translationService.getTranslations();
  }

  ngOnInit(): void {
    // Subscribe to warehouse name changes
    this.warehouseNameService.warehouseName$.subscribe(name => {
      this.warehouseName = name;
      // Update page name when warehouse name changes
      this.updatePageName(this.router.url);
    });

    // Subscribe to translation changes
    this.translationService.currentLanguage$.subscribe(() => {
      this.t = this.translationService.getTranslations();
      console.log('App component translations updated:', this.t);
      // Update page name when translations change
      this.updatePageName(this.router.url);
    });

    // Subscribe to router events to update page name
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      this.updatePageName(event.url);
    });

    // Set initial page name
    this.updatePageName(this.router.url);

    // Wait a bit for translations to load, then check if this is the first run
    setTimeout(() => {
      if (!this.warehouseNameService.hasWarehouseDevice()) {
        this.showWarehouseNameDialog();
      }
    }, 100);
  }

  private updatePageName(url: string): void {
    if (url.includes('/product')) {
      this.currentPageName = this.t['products'];
    } else if (url.includes('/calculator')) {
      this.currentPageName = this.t['products'] + ' / ' + this.t['print'];
    } else {
      this.currentPageName = this.t['products']; // Default to Products
    }
  }



  showWarehouseNameDialog(): void {
    const dialogRef = this.dialog.open(WarehouseNameDialogComponent, {
      width: '450px',
      disableClose: true, // User must select a device or cancel
      data: {
        existingName: this.warehouseNameService.getWarehouseName(),
        existingId: this.warehouseNameService.getWarehouseDeviceId()
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.id && result.name) {
        // Result is now a TouchscreenDevice object with id and name
        this.warehouseNameService.setWarehouseDevice(result);
      }
    });
  }
}