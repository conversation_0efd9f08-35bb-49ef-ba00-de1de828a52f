{"name": "warehouse-calculator-app", "version": "0.0.0", "main": "main.js", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build-prod": "ng build --configuration=production", "watch": "ng build --watch --configuration development", "test": "ng test", "electron": "set ELECTRON_IS_DEV=1 && electron .", "electron-dev": "concurrently \" npx ng serve --port 4201\" \"set ELECTRON_IS_DEV=1 && set ANGULAR_ENV=localhost:4201 && electron .\"", "electron-build-prod": "ng build --configuration=production --base-href ./ && set ELECTRON_IS_DEV=0 && electron .", "packager": "electron-packager . --platform=win32 --overwrite --out=electron-packager"}, "private": true, "dependencies": {"@angular/animations": "^15.2.0", "@angular/common": "^15.2.0", "@angular/compiler": "^15.2.0", "@angular/core": "^15.2.0", "@angular/forms": "^15.2.0", "@angular/localize": "^15.2.10", "@angular/material": "^15.2.6", "@angular/platform-browser": "^15.2.0", "@angular/platform-browser-dynamic": "^15.2.0", "@angular/router": "^15.2.0", "@auth0/angular-jwt": "^5.2.0", "@electron/remote": "^2.1.2", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "date-fns": "^3.6.0", "edge-cs": "^1.2.1", "electron-edge-js": "^30.0.2", "electron-is-dev": "^3.0.1", "electron-packager": "^17.1.2", "electron-rebuild": "^3.2.9", "mocha": "^10.4.0", "mochawesome": "^7.1.3", "nan": "^2.19.0", "ngx-countdown": "^13.0.0", "ngx-electron": "^2.2.0", "ngx-mask": "^14.3.3", "ngx-translate-messageformat-compiler": "^6.2.0", "node-hid": "^3.0.0", "rxjs": "~7.8.0", "simple-keyboard": "^3.7.87", "simple-keyboard-input-mask": "^3.0.526", "tslib": "^2.3.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.5", "@angular/cli": "~15.2.5", "@angular/compiler-cli": "^15.2.0", "@types/jasmine": "~4.3.0", "@types/node": "^20.12.12", "concurrently": "^9.0.1", "cross-env": "^7.0.3", "electron": "^30.0.3", "electron-builder": "^25.1.8", "electron-reload": "^2.0.0-alpha.1", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.9.4", "wait-on": "^8.0.1"}, "browser": {"fs": false, "path": false, "os": false, "crypto": false, "stream": false, "http": false, "https": false, "url": false, "querystring": false}}