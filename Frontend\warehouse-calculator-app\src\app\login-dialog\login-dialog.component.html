<div class="login-dialog-container">
  <h2 mat-dialog-title><strong class="title-with-icon"><mat-icon>person</mat-icon> {{ t['login'] || 'Вход' }} - {{ t['user'] || 'Потребител' }}</strong></h2>
  
  <mat-dialog-content>
    <div class="form-fields">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>{{ t['username'] || 'Потребителско име' }}</mat-label>
        <input matInput [(ngModel)]="username" required>
      </mat-form-field>
      
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>{{ t['password'] || 'Парола' }}</mat-label>
        <input matInput type="password" [(ngModel)]="password" required>
      </mat-form-field>
    </div>
  </mat-dialog-content>
  
  <mat-dialog-actions class="button-container">
    <button mat-raised-button class="cancel-button" (click)="onCancel()">{{ t['cancel'] || 'Отказ' }}</button>
    <button mat-raised-button class="accept-button" (click)="onAccept()" [disabled]="!isFormValid()">
      {{ t['accept'] || 'Приемам' }}
    </button>
  </mat-dialog-actions>
</div> 