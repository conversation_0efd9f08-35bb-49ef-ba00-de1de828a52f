import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Product } from '../product.model';

@Component({
  selector: 'app-product-card',
  templateUrl: './product-card.component.html',
  styleUrls: ['./product-card.component.css']
})
export class ProductCardComponent {
  @Input() product!: Product;
  @Output() cardClick = new EventEmitter<Product>();

  constructor() { }

  onCardClick(): void {
    this.cardClick.emit(this.product);
  }

  onImageError(event: any): void {
    // Set a default placeholder image if the product image fails to load
    event.target.src = 'assets/images/tile.jpg';
    // Add a class to indicate this is a fallback image
    event.target.classList.add('fallback-image');
  }
} 