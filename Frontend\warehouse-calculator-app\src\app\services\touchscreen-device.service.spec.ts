import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TouchscreenDeviceService, DropdownDto } from './touchscreen-device.service';
import { environment } from '../../environments/environment';

describe('TouchscreenDeviceService', () => {
  let service: TouchscreenDeviceService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [TouchscreenDeviceService]
    });
    service = TestBed.inject(TouchscreenDeviceService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should fetch touchscreen devices list', () => {
    const mockDevices: DropdownDto[] = [
      { id: 1, name: 'Device 1' },
      { id: 2, name: 'Device 2' }
    ];

    service.getTouchscreenDevicesList().subscribe(devices => {
      expect(devices).toEqual(mockDevices);
    });

    const req = httpMock.expectOne(`${environment.apiUrl}Devices/List`);
    expect(req.request.method).toBe('GET');
    req.flush(mockDevices);
  });

  it('should return mock data when backend is not available', () => {
    service.getTouchscreenDevicesList().subscribe(devices => {
      expect(devices).toBeDefined();
      expect(devices.length).toBeGreaterThan(0);
      expect(devices[0].id).toBeDefined();
      expect(devices[0].name).toBeDefined();
      expect(typeof devices[0].id).toBe('number');
      expect(typeof devices[0].name).toBe('string');
    });

    const req = httpMock.expectOne(`${environment.apiUrl}Devices/List`);
    req.error(new ProgressEvent('Network error'), { status: 0 });
  });
});
