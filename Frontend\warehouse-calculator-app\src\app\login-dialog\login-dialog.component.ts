import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslationService, TranslationKeys } from '../services/translation.service';

export interface LoginDialogData {
  username?: string;
  password?: string;
}

@Component({
  selector: 'app-login-dialog',
  templateUrl: './login-dialog.component.html',
  styleUrls: ['./login-dialog.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    FormsModule
  ]
})
export class LoginDialogComponent {
  username: string = '';
  password: string = '';
  t: TranslationKeys;

  constructor(
    public dialogRef: MatDialogRef<LoginDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: LoginDialogData,
    private translationService: TranslationService
  ) {
    this.t = this.translationService.getTranslations();
    this.username = data.username || '';
    this.password = data.password || '';
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onAccept(): void {
    if (this.username.trim() && this.password.trim()) {
      this.dialogRef.close({
        username: this.username,
        password: this.password
      });
    }
  }

  isFormValid(): boolean {
    return this.username.trim().length > 0 && this.password.trim().length > 0;
  }
} 