<div class="warehouse-dialog-container">
  <!-- <h2 style="text-align: center;" mat-dialog-title>
    {{ t['welcomeToWarehouseCalculator'] || 'Добре дошли в Калкулатора за склад' }}
  </h2> -->

  <mat-dialog-content>
    <!-- Device selection dropdown - show immediately when devices are available -->
    <mat-form-field *ngIf="touchscreenDevices.length > 0" appearance="outline" class="warehouse-name-field">
      <mat-label>{{ t['touchscreenDevice'] || 'Устройство' }}</mat-label>
      <mat-select
        [(ngModel)]="selectedDevice"
        (selectionChange)="onDeviceSelectionChange()"
        [compareWith]="compareDevices">
        <mat-option *ngFor="let device of touchscreenDevices" [value]="device">
          {{ device.name }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <!-- Only show minimal loading when no devices are available yet -->
    <div *ngIf="touchscreenDevices.length === 0 && isLoading" class="loading-container">
      <mat-spinner diameter="30"></mat-spinner>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <!-- <button mat-button (click)="onCancel()">Cancel</button> -->
    <button mat-button="filled" (click)="onSave()" [disabled]="!isFormValid() || isLoading">
      {{ t['save'] || 'Запази' }}
    </button>
  </mat-dialog-actions>
</div>