.product-container {
  padding: 15px;
  max-width: 1400px;
  margin: 0 auto;
}

.product-header {
  text-align: center;
  margin-bottom: 15px;
}

.product-header h1 {
  color: #333;
  margin-bottom: 8px;
  font-size: 2.5rem;
  font-weight: 300;
}

.product-header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
  padding: 0;
}

/* Responsive design for different screen sizes */
@media (max-width: 1200px) {
  .product-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 992px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .product-container {
    padding: 15px;
  }
  
  .product-header h1 {
    font-size: 2rem;
  }
}

.product-controls {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.search-section {
  flex: 1;
  min-width: 300px;
  margin-top: 0px;
}

.search-container {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.search-field-small {
  width: 250px;
}

.search-button {
  height: 56px; /* Match the height of the form field */
  margin-left: 10px;
  margin-top: 0; /* Push button down to align with form field input area */
}

.clear-search-button {
  height: 56px; /* Match the height of the form field */
  margin-left: 10px;
  margin-top: 0; /* Push button down to align with form field input area */
}

.history-dropdown {
  width: 250px;
}

.search-field {
  width: 100%;
}

.pagination-section {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.pagination-button {
  display: flex;
  align-items: center;
  gap: 5px;
}

.page-info {
  font-weight: 500;
  color: #666;
  min-width: 100px;
  text-align: center;
}

@media (max-width: 480px) {
  .product-grid {
    grid-template-columns: 1fr;
  }
  
  .product-container {
    padding: 10px;
  }
  
  .product-header h1 {
    font-size: 1.8rem;
  }

  .product-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-section {
    min-width: auto;
  }

  .pagination-section {
    justify-content: center;
  }
} 