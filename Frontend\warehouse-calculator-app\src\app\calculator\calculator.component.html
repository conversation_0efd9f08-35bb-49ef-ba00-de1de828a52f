<div class="calculator-container">
  <!-- Calculator Section -->
  <mat-card class="calculator-card">
    <!-- Product Header Section -->
    <div *ngIf="selectedProduct" class="product-header">
      <!-- Product Name + Code (like sequential number in image) -->
      <div class="product-title">
        {{ selectedProduct.name }} - {{ selectedProduct.code }}
      </div>
      
      <!-- Product Details Row -->
      <div class="product-details-row">
        <div class="product-detail-item">
          <span class="label">{{ t['productCodeLabel'] || 'Арт. Номер' }}:</span>
          <span class="value">{{ selectedProduct.code }}</span>
        </div>
        <div class="product-detail-item">
          <span class="label">{{ t['sequentialNumber'] || 'Ном. Код' }}:</span>
          <span class="value">{{ selectedProduct.sequentialNumber || 'N/A' }}</span>
        </div>
        <div class="product-detail-item">
          <span class="label">{{ t['barcode'] || 'Баркод' }}:</span>
          <span class="value">{{ selectedProduct.barcode || 'N/A' }}</span>
        </div>
      </div>
    </div>
    
    <mat-card-content>
      <div *ngIf="selectedProduct">
          
          <!-- Calculator Input Fields -->
          <div class="calculator-inputs">
            <mat-form-field appearance="outline" class="input-field">
              <mat-label>{{ t['package'] }}</mat-label>
              <input 
                matInput 
                type="number" 
                [(ngModel)]="package" 
                [placeholder]="t['enterPackageValue']"
                min="0"
                step="0.01"
              >
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="input-field">
              <mat-label>{{ t['tile'] }}</mat-label>
              <input 
                matInput 
                type="number" 
                [(ngModel)]="tile" 
                [placeholder]="t['enterTileValue']"
                min="0"
                step="0.01"
              >
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="input-field">
              <mat-label>{{ t['m2'] }}</mat-label>
              <input 
                matInput 
                type="number" 
                [(ngModel)]="m2" 
                [placeholder]="t['enterM2Value']"
                min="0"
                step="0.01"
              >
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="input-field">
              <mat-label>{{ t['totalM2'] }}</mat-label>
              <input 
                matInput 
                type="number" 
                [(ngModel)]="totalM2" 
                [placeholder]="t['totalM2Value']"
                min="0"
                step="0.01"
                readonly
              >
            </mat-form-field>
          </div>
          
          <!-- Calculator Buttons -->
          <div class="calculator-buttons">
            <button 
              mat-raised-button 
              color="warn" 
              (click)="onCancel()"
              class="cancel-button"
            >
              <mat-icon>cancel</mat-icon>
              {{ t['cancel'] }}
            </button>
            
            <button 
              mat-raised-button 
              color="primary" 
              (click)="onPrint()"
              class="print-button"
            >
              <mat-icon>print</mat-icon>
              {{ t['printButton'] }}
            </button>
          </div>
        </div>
    </mat-card-content>
  </mat-card>
</div>