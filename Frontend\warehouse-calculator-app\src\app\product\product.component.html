<div class="product-container">
  
  <div class="product-grid">
    <app-product-card 
      *ngFor="let product of paginatedProducts" 
      [product]="product"
      (cardClick)="onProductClick($event)"
    ></app-product-card>
  </div>

  <div class="product-controls">
    <div class="search-section">
      <div class="search-container">
        <mat-form-field appearance="outline" class="search-field-small">
          <mat-label>{{ t['searchByCodeOrName'] }}</mat-label>
          <input 
            matInput 
            [(ngModel)]="searchTerm" 
            (focus)="onSearchFocus()"
            (blur)="onSearchBlur()"
            (keyup.enter)="onSearchButtonClick()"
            placeholder="{{ t['enterProductCodeOrName'] }}"
          >
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        
        <button 
          mat-raised-button 
          color="primary" 
          (click)="onSearchButtonClick()"
          class="search-button"
        >
          <mat-icon>search</mat-icon>
          {{ t['search'] || 'Search' }}
        </button>
        
                <button 
          mat-raised-button 
          color="warn" 
          (click)="onClearSearch()"
          class="clear-search-button"
          *ngIf="searchTerm?.trim()"
        >
          <mat-icon>clear</mat-icon>
          {{ t['clearSearch'] || 'Clear' }}
        </button>
       </div>
     </div>
     
     <div class="pagination-section">
       <mat-form-field appearance="outline" class="history-dropdown">
         <mat-label>{{ t['recentSearches'] || 'Recent Searches' }}</mat-label>
         <mat-select 
           [(ngModel)]="searchTerm" 
           (selectionChange)="selectFromHistory($event)"
         >
           <mat-option *ngFor="let term of searchHistory" [value]="term">
             {{ term }}
           </mat-option>
           <mat-option *ngIf="searchHistory.length === 0" disabled>
             {{ t['noRecentSearches'] || 'No recent searches' }}
           </mat-option>
         </mat-select>
         <mat-icon matSuffix>history</mat-icon>
       </mat-form-field>
      <button 
        mat-button 
        [disabled]="!canGoPrevious()" 
        (click)="previousPage()"
        class="pagination-button"
      >
        <mat-icon>chevron_left</mat-icon>
        {{ t['previous'] }}
      </button>
      
      <span class="page-info">
        {{ t['page'] }} {{ currentPage }} {{ t['of'] }} {{ totalPages }}
      </span>
      
      <button 
        mat-button 
        [disabled]="!canGoNext()" 
        (click)="nextPage()"
        class="pagination-button"
      >
        {{ t['next'] }}
        <mat-icon>chevron_right</mat-icon>
      </button>
    </div>
  </div>
</div> 