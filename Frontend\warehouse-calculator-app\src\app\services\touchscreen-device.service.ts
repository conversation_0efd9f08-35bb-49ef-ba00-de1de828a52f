import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface DropdownDto {
  id: number;
  name: string;
}

@Injectable({
  providedIn: 'root'
})
export class TouchscreenDeviceService {
  private readonly apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  getTouchscreenDevicesList(): Observable<DropdownDto[]> {
    console.log('Fetching touchscreen devices from:', `${this.apiUrl}Devices/List`);

    return this.http.get<DropdownDto[]>(`${this.apiUrl}Devices/List`).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Error fetching touchscreen devices:', error);

        // Don't use mock data for authentication errors
        if (error.status === 401 || error.status === 403) {
          console.warn('Authentication required for touchscreen devices');
          return throwError(() => error);
        }

        // For development purposes, return mock data if backend is not available
        if (error.status === 0 || error.status >= 500) {
          console.warn('Backend not available, using mock data for development');
          return of(this.getMockDevices());
        }

        return throwError(() => error);
      })
    );
  }

  private getMockDevices(): DropdownDto[] {
    return [
      { id: 1, name: 'Touchscreen Device 1' },
      { id: 2, name: 'Touchscreen Device 2' },
      { id: 3, name: 'Touchscreen Device 3' },
      { id: 4, name: 'Main Warehouse Terminal' },
      { id: 5, name: 'Secondary Terminal' }
    ];
  }
}
