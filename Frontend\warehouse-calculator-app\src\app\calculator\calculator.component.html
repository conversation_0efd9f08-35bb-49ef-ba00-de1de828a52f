<div class="calculator-container">
  <!-- Product Information Section -->
  <div *ngIf="showProductInfo && selectedProduct" class="product-info-section">
    <mat-card class="product-info-card">
             <mat-card-header>
         <mat-card-title>{{ selectedProduct.name }}</mat-card-title>
         <mat-card-subtitle>{{ selectedProduct.code }}</mat-card-subtitle>
       </mat-card-header>
      
      <mat-card-content>
        <div class="product-details">
          <div class="product-image-container">
            <img 
              [src]="selectedProduct.imageUrl" 
              [alt]="selectedProduct.name"
              class="product-image"
            />
          </div>
          
          <div class="product-text-info">
            <p *ngIf="selectedProduct.sequentialNumber" class="product-sequential-number">
              {{ selectedProduct.sequentialNumber }}
            </p>
            <p *ngIf="selectedProduct.category" class="product-category">
              <strong>Category:</strong> {{ selectedProduct.category }}
            </p>
            
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Calculator Section -->
  <mat-card class="calculator-card">
    <mat-card-header>
      <mat-card-title>
        <span *ngIf="selectedProduct">{{ t['calculatorFor'] }} {{ selectedProduct.name }}</span>
      </mat-card-title>
    </mat-card-header>
    
           <mat-card-content>
              <div *ngIf="selectedProduct">
          <p>Calculator interface for {{ selectedProduct.name }} ({{ selectedProduct.code }})</p>
          
          <!-- Product Data Display (for reference) -->
          <div class="product-data-reference">
            <p><strong>{{ t['productDataAvailableForCalculations'] }}</strong></p>
            <ul>
              <li>{{ t['productId'] }}: {{ selectedProduct.id }}</li>
              <li>{{ t['productNameLabel'] }}: {{ selectedProduct.name }}</li>
              <li>{{ t['productCodeLabel'] }}: {{ selectedProduct.code }}</li>
              <li>{{ t['sequentialNumber'] }}: {{ selectedProduct.sequentialNumber }}</li>
              <li>{{ t['categoryLabel'] }}: {{ selectedProduct.category }}</li>
            </ul>
          </div>
          
          <!-- Calculator Input Fields -->
          <div class="calculator-inputs">
            <mat-form-field appearance="outline" class="input-field">
              <mat-label>{{ t['package'] }}</mat-label>
              <input 
                matInput 
                type="number" 
                [(ngModel)]="package" 
                [placeholder]="t['enterPackageValue']"
                min="0"
                step="0.01"
              >
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="input-field">
              <mat-label>{{ t['tile'] }}</mat-label>
              <input 
                matInput 
                type="number" 
                [(ngModel)]="tile" 
                [placeholder]="t['enterTileValue']"
                min="0"
                step="0.01"
              >
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="input-field">
              <mat-label>{{ t['m2'] }}</mat-label>
              <input 
                matInput 
                type="number" 
                [(ngModel)]="m2" 
                [placeholder]="t['enterM2Value']"
                min="0"
                step="0.01"
              >
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="input-field">
              <mat-label>{{ t['totalM2'] }}</mat-label>
              <input 
                matInput 
                type="number" 
                [(ngModel)]="totalM2" 
                [placeholder]="t['totalM2Value']"
                min="0"
                step="0.01"
                readonly
              >
            </mat-form-field>
          </div>
          
          <!-- Calculator Buttons -->
          <div class="calculator-buttons">
            <button 
              mat-raised-button 
              color="warn" 
              (click)="onCancel()"
              class="cancel-button"
            >
              <mat-icon>cancel</mat-icon>
              {{ t['cancel'] }}
            </button>
            
            <button 
              mat-raised-button 
              color="primary" 
              (click)="onPrint()"
              class="print-button"
            >
              <mat-icon>print</mat-icon>
              {{ t['printButton'] }}
            </button>
          </div>
        </div>
    </mat-card-content>
  </mat-card>
</div>