import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

export interface TranslationKeys {
  [key: string]: string;
}

@Injectable({
  providedIn: 'root'
})
export class TranslationService {
  private currentLanguageSubject = new BehaviorSubject<string>('bg');
  public currentLanguage$ = this.currentLanguageSubject.asObservable();
  private translations: { [key: string]: TranslationKeys } = {};

  constructor(private http: HttpClient) {
    // Load saved language preference
    const savedLanguage = localStorage.getItem('preferred_language');
    if (savedLanguage) {
      this.currentLanguageSubject.next(savedLanguage);
    } else {
      // Set Bulgarian as default if no language preference is saved
      localStorage.setItem('preferred_language', 'bg');
    }
    
    // Load translation files
    this.loadTranslations();
  }

  private loadTranslations(): void {
    console.log('Loading translations...');
    // Load Bulgarian translations first (default)
    this.http.get<TranslationKeys>('assets/i18n/bg.json').pipe(
      catchError(error => {
        console.error('Failed to load Bulgarian translations:', error);
        // Load fallback translations
        this.loadFallbackTranslations();
        throw error;
      })
    ).subscribe(
      translations => {
        console.log('Bulgarian translations loaded:', translations);
        this.translations['bg'] = translations;
        // If current language is Bulgarian, update the translations
        if (this.getCurrentLanguage() === 'bg') {
          this.currentLanguageSubject.next('bg');
        }
      }
    );

    // Load English translations
    this.http.get<TranslationKeys>('assets/i18n/en.json').pipe(
      catchError(error => {
        console.error('Failed to load English translations:', error);
        throw error;
      })
    ).subscribe(
      translations => {
        this.translations['en'] = translations;
        // If current language is English, update the translations
        if (this.getCurrentLanguage() === 'en') {
          this.currentLanguageSubject.next('en');
        }
      }
    );
  }

  private loadFallbackTranslations(): void {
    // Fallback translations in case JSON files fail to load (Bulgarian as default)
    this.translations['bg'] = {
      warehouseCalculatorApp: 'Приложение за изчисляване на склад',
      products: 'Продукти',
      print: 'Печат',
      browseProducts: 'Разглеждане на продукти',
      productName: 'Име на продукт',
      productCode: 'Код на продукт',
      sequentialNumber: 'Пореден номер',
      category: 'Категория',
      searchByCodeOrName: 'Търсене по код или име',
      enterProductCodeOrName: 'Въведете код или име на продукт...',
      previous: 'Предишен',
      next: 'Следващ',
      page: 'Страница',
      of: 'от',
      calculator: 'Калкулатор',
      calculatorFor: 'Калкулатор за',
      package: 'Пакет',
      tile: 'Плочка',
      m2: 'М²',
      totalM2: 'Общо М²',
      cancel: 'Отказ',
      printButton: 'Печат',
      enterPackageValue: 'Въведете стойност на пакет',
      enterTileValue: 'Въведете стойност на плочка',
      enterM2Value: 'Въведете стойност в м²',
      totalM2Value: 'Общо м²',
      welcomeToWarehouseCalculator: 'Добре дошли в Калкулатора за склад',
      enterWarehouseName: 'Моля, въведете името на вашия склад:',
      warehouseName: 'Име на склад',
      enterWarehouseNamePlaceholder: 'Въведете име на склад...',
      maximum50Characters: 'Максимум 50 символа',
      save: 'Запази',
      productDataAvailableForCalculations: 'Данни за продукт, налични за изчисления:',
      productId: 'ID на продукт',
      productNameLabel: 'Име на продукт',
      productCodeLabel: 'Код на продукт',
      categoryLabel: 'Категория',
                        welcomeToWarehouseCalculatorApp: 'Добре дошли в Приложението за изчисляване на склад!',
                  selectProductToStartCalculating: 'Моля, отидете на страницата Продукти и изберете продукт, за да започнете изчисленията.',
                  goToProductsPage: 'Разглеждане на продукти',
                  login: 'Вход',
                  username: 'Потребителско име',
                  password: 'Парола',
                  enterUsername: 'Въведете потребителско име',
                  enterPassword: 'Въведете парола',
                  accept: 'Приемам',
                  user: 'Потребител',
                  recentSearches: 'Последни търсения',
                  noRecentSearches: 'Няма последни търсения',
                  selectTouchscreenDevice: 'Моля, изберете вашето устройство:',
                  touchscreenDevice: 'Устройство',
                  loadingDevices: 'Зареждане на устройства...',
                  retry: 'Опитай отново',
                  authenticationRequired: 'Необходима е автентификация за зареждане на устройствата.',
                  loginFailed: 'Неуспешен вход. Моля, проверете данните си и опитайте отново.',
                  failedToLoadDevices: 'Неуспешно зареждане на устройствата. Моля, опитайте отново.'
    };
    
    this.currentLanguageSubject.next('bg');
  }

  getCurrentLanguage(): string {
    return this.currentLanguageSubject.value;
  }

  setLanguage(language: string): void {
    if (this.translations[language]) {
      this.currentLanguageSubject.next(language);
      localStorage.setItem('preferred_language', language);
    }
  }

  getTranslation(key: string): string {
    const currentLang = this.getCurrentLanguage();
    return this.translations[currentLang]?.[key] || this.translations['bg']?.[key] || key;
  }

  getTranslations(): TranslationKeys {
    const currentLang = this.getCurrentLanguage();
    return this.translations[currentLang] || this.translations['bg'] || {};
  }

  getAvailableLanguages(): string[] {
    return Object.keys(this.translations);
  }
} 