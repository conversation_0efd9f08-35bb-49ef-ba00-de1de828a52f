import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface TouchscreenDevice {
  id: number;
  name: string;
}

@Injectable({
  providedIn: 'root'
})
export class WarehouseNameService {
  private readonly STORAGE_KEY_NAME = 'warehouse_name';
  private readonly STORAGE_KEY_ID = 'warehouse_device_id';
  private warehouseNameSubject = new BehaviorSubject<string>('');
  private warehouseDeviceIdSubject = new BehaviorSubject<number | null>(null);

  public warehouseName$ = this.warehouseNameSubject.asObservable();
  public warehouseDeviceId$ = this.warehouseDeviceIdSubject.asObservable();

  constructor() {
    this.loadWarehouseData();
  }

  private loadWarehouseData(): void {
    const storedName = localStorage.getItem(this.STORAGE_KEY_NAME);
    const storedId = localStorage.getItem(this.STORAGE_KEY_ID);

    if (storedName) {
      this.warehouseNameSubject.next(storedName);
    }

    if (storedId) {
      this.warehouseDeviceIdSubject.next(parseInt(storedId, 10));
    }
  }

  getWarehouseName(): string {
    return this.warehouseNameSubject.value;
  }

  getWarehouseDeviceId(): number | null {
    return this.warehouseDeviceIdSubject.value;
  }

  setWarehouseDevice(device: TouchscreenDevice): void {
    localStorage.setItem(this.STORAGE_KEY_NAME, device.name);
    localStorage.setItem(this.STORAGE_KEY_ID, device.id.toString());
    this.warehouseNameSubject.next(device.name);
    this.warehouseDeviceIdSubject.next(device.id);
  }

  hasWarehouseName(): boolean {
    return !!this.getWarehouseName();
  }

  hasWarehouseDevice(): boolean {
    return !!this.getWarehouseName() && this.getWarehouseDeviceId() !== null;
  }

  clearWarehouseData(): void {
    localStorage.removeItem(this.STORAGE_KEY_NAME);
    localStorage.removeItem(this.STORAGE_KEY_ID);
    this.warehouseNameSubject.next('');
    this.warehouseDeviceIdSubject.next(null);
  }

  // Legacy method for backward compatibility
  setWarehouseName(name: string): void {
    localStorage.setItem(this.STORAGE_KEY_NAME, name);
    this.warehouseNameSubject.next(name);
  }

  // Legacy method for backward compatibility
  clearWarehouseName(): void {
    this.clearWarehouseData();
  }
}