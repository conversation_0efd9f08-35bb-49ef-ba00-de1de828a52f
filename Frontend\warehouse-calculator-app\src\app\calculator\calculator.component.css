.calculator-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.product-info-section {
  margin-bottom: 20px;
}

.product-info-card {
  background-color: #f8f9fa;
  border-left: 4px solid #1976d2;
}

.product-info-card mat-card-header {
  position: relative;
}

.close-button {
  position: absolute;
  top: 8px;
  right: 8px;
}

.product-details {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.product-image-container {
  flex-shrink: 0;
  width: 150px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.product-text-info {
  flex: 1;
}

.product-sequential-number {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 8px 0;
  text-align: center;
  line-height: 1.2;
}

.product-category,
.product-price {
  margin: 5px 0;
  font-size: 14px;
}

.calculator-card {
  margin-bottom: 20px;
}

.calculator-card ul {
  margin: 15px 0;
  padding-left: 20px;
}

 .calculator-card li {
   margin: 5px 0;
   font-size: 14px;
 }

 .calculator-inputs {
   display: flex;
   gap: 15px;
   margin: 20px 0;
   flex-wrap: wrap;
 }

 .input-field {
   flex: 1;
   min-width: 200px;
 }

 .calculator-buttons {
   display: flex;
   justify-content: space-between;
   margin: 20px 0;
   gap: 15px;
 }

 .cancel-button,
 .print-button {
   min-width: 120px;
   height: 45px;
   font-size: 16px;
 }

 .product-data-reference {
   margin-top: 30px;
   padding: 15px;
   background-color: #f5f5f5;
   border-radius: 8px;
   border-left: 4px solid #1976d2;
 }

 /* Responsive design */
 @media (max-width: 768px) {
   .product-details {
     flex-direction: column;
     gap: 15px;
   }
   
   .product-image-container {
     width: 100%;
     height: 120px;
   }

   .calculator-inputs {
     flex-direction: column;
   }

   .input-field {
     min-width: auto;
   }

   .calculator-buttons {
     flex-direction: column;
   }

   .cancel-button,
   .print-button {
     width: 100%;
   }
 }