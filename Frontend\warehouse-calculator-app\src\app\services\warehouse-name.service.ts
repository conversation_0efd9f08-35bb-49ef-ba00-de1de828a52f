import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class WarehouseNameService {
  private readonly STORAGE_KEY = 'warehouse_name';
  private warehouseNameSubject = new BehaviorSubject<string>('');
  public warehouseName$ = this.warehouseNameSubject.asObservable();

  constructor() {
    this.loadWarehouseName();
  }

  private loadWarehouseName(): void {
    const storedName = localStorage.getItem(this.STORAGE_KEY);
    if (storedName) {
      this.warehouseNameSubject.next(storedName);
    }
  }

  getWarehouseName(): string {
    return this.warehouseNameSubject.value;
  }

  setWarehouseName(name: string): void {
    localStorage.setItem(this.STORAGE_KEY, name);
    this.warehouseNameSubject.next(name);
  }

  hasWarehouseName(): boolean {
    return !!this.getWarehouseName();
  }

  clearWarehouseName(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    this.warehouseNameSubject.next('');
  }
} 