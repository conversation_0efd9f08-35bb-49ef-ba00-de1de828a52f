<mat-card class="product-card" (click)="onCardClick()">
  <div class="product-name-section">
    <h3 class="product-name">{{ product.name }}</h3>
    <p class="product-code">{{ product.code }}</p>
  </div>
  
  <div class="product-image-container">
    <img 
      [src]="product.imageUrl" 
      [alt]="product.name"
      class="product-image"
      (error)="onImageError($event)"
    />
  </div>
  
  <div class="product-sequential-number-section">
    <p *ngIf="product.sequentialNumber" class="product-sequential-number">
      {{ product.sequentialNumber }}
    </p>
  </div>
</mat-card> 