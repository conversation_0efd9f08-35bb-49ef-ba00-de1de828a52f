<div class="warehouse-dialog-container">
  <!-- <h2 style="text-align: center;" mat-dialog-title>
    {{ t['welcomeToWarehouseCalculator'] || 'Добре дошли в Калкулатора за склад' }}
  </h2> -->
  
  <mat-dialog-content>
    <p style="text-align: center;">
      {{ t['enterWarehouseName'] || 'Моля, въведете името на вашия склад:' }}
    </p>
    
    <mat-form-field appearance="outline" class="warehouse-name-field">
      <mat-label>{{ t['warehouseName'] || 'Име на склад' }}</mat-label>
      <input 
        matInput 
        [(ngModel)]="warehouseName" 
        [placeholder]="t['enterWarehouseNamePlaceholder'] || 'Въведете име на склад...'"
        maxlength="500"
        autofocus
      >
    </mat-form-field>
  </mat-dialog-content>
  
  <mat-dialog-actions align="end">
    <!-- <button mat-button (click)="onCancel()">Cancel</button> -->
    <button mat-button="filled" (click)="onSave()" [disabled]="!isFormValid()">
      {{ t['save'] || 'Запази' }}
    </button>
  </mat-dialog-actions>
</div> 